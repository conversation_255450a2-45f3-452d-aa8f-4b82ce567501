//
//  AppDelegate.swift
//  sample_app_tudo
//
//  Created by <PERSON> Do on 27/09/2022.
//

import UIKit
import CommonCrypto // copy and replace asset file base on sha256 value

@main
class AppDelegate: UIResponder, UIApplicationDelegate, VGuardManagerProtocol, VGuardThreatsDelegate, VGuardExceptionHandlerProtocol, NSURLConnectionDataDelegate {
    var window: UIWindow?
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // not use sceneDelegate
        window = UIWindow(frame: UIScreen.main.bounds)
        // Load from Main.storyboard
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        let initialViewController = storyboard.instantiateInitialViewController()
        window?.rootViewController = initialViewController
        window?.makeKeyAndVisible()
        
        
//        simulate8iOS() // NOTE: simulate -8 error
        // config vkey first before trigger start
        if #available(iOS 13.4, *) {
            checkVKeyAssetsAndReplace()
        }
        VkeyConfiguration()
        return true
    }
    
    // declare VGuardManager
    var vGuardManager: VGuardManager! = nil
    var resetAttemptCount: Int = 0  // Counter for reset attempts per app launch

    // VKeyExtension instance - all logging logic is now handled here
    lazy var logManager: VKeyExtension = {
        let manager = VKeyExtension.sharedManager()
        manager.setupLegacyNotificationSupport() // Setup for existing UI compatibility
        return manager
    }()

    // Time measurement for vGuard scan performance
    private var vGuardScanStartTime: CFAbsoluteTime?
    
    // All logging methods now delegate to VKeyExtension
    func sentLogEvent(_ logText: String,
                      textColor: UIColor = .black,
                      backgroundColor: UIColor = .clear,
                      fontSize: CGFloat = 14,
                      isBold: Bool = false,
                      isItalic: Bool = false) {
        logManager.sentLogEvent(logText,
                               textColor: textColor,
                               backgroundColor: backgroundColor,
                               fontSize: fontSize,
                               isBold: isBold,
                               isItalic: isItalic)
    }
    
    func simulate8iOS() {
        let fileManager = FileManager.default
        if let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentsDirectory.appendingPathComponent("IRK.enc")
            
            do {
                if fileManager.fileExists(atPath: fileURL.path) {
                    try fileManager.removeItem(at: fileURL)
                    sentLogEventDebug("simulate8iOS: Deleted file at path: \(fileURL.path)")
                } else {
                    sentLogEventDebug("can't find IRK.enc, maybe it's already deleted?")
                }
            } catch {
                print("Error deleting IRK.enc file at path: \(fileURL.path) - \(error)")
            }
        }
    }
    
    func VkeyConfiguration() {
        // Start time measurement
        vGuardScanStartTime = CFAbsoluteTimeGetCurrent()
        logManager.logSuccess("🚀 vGuard init started - measuring performance...")
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self else { return }
            vGuardManager = VGuardManager.shared()
            vGuardManager.delegate = self
            vGuardManager.setMemoryConfiguration(VOSMemoryConfiguration(rawValue: 1)!)
//            vGuardManager.isDebug = true //NOTE: only enable it when debugging
            
            //        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            //            guard let self else { return }
            
            // NOTE: Set this if your project use Ti feature (threat-intelligent)
            vGuardManager.setThreatIntelligenceServerURL("https://stg-cloud.v-key.com")
            // NOTE: Set this if your project use TLA feature (troubleshooting log analysing)
            VosWrapper.setLoggerBaseUrl("https://stg-cloud.v-key.com")
            //        }
            
            VGuardExceptionHandler.sharedManager()?.delegate = self
            VGuardThreats.sharedModule()?.delegate = self
            //        VGuardExceptionHandler.sharedManager().allowSendStacktraceLog = false
            
            sentLogEvent("initialize VGuard.....")
            _ = self.vGuardManager.initializeVGuard()
            self.vGuardManager.start()
        }
    }
    
    func clearUILog() {
        logManager.clearUILog()
    }

    func trigerStartScan() {
        // trigger test performance from here
        if let manager = vGuardManager {
//            // Start time measurement
//            vGuardScanStartTime = CFAbsoluteTimeGetCurrent()
//            logManager.logPerformance("🚀 vGuard scan started - measuring performance...")

            manager.start()
        }
    }
    func getTroubleshootingID() { sentLogEvent("TroubleshootingId: \(vGuardManager.getTroubleshootingId())") }

    // All convenience methods now delegate to VKeyExtension
    func sentLogEventSuccess(_ logText: String) {
        logManager.sentLogEventSuccess(logText)
    }

    func sentLogEventError(_ logText: String) {
        logManager.sentLogEventError(logText)
    }

    func sentLogEventWarning(_ logText: String) {
        logManager.sentLogEventWarning(logText)
    }

    func sentLogEventInfo(_ logText: String) {
        logManager.sentLogEventInfo(logText)
    }

    func sentLogEventDebug(_ logText: String) {
        logManager.sentLogEventDebug(logText)
    }

    func sentLogEventHighlight(_ logText: String) {
        logManager.sentLogEventHighlight(logText)
    }
    
    // MARK: handle trigger start scan whenever app move to forceground stage
    func applicationDidBecomeActive(_ application: UIApplication) {
        print("^applicationDidBecomeActive")
        trigerStartScan()
    }
    
    // MARK: VGuardManagerProtocol
    func statusVOS(_ status: VOS_STATUS, withError error: Error?) {
        sentLogEvent("statusVOS withError: \((error as NSError?)?.code ?? 0)")
        
        
        
        if (status == VOS_OK) {
            sentLogEvent("statusVOS: VOS_OK")
            getTroubleshootingID()
            sentLogEvent("bundleID: \(String(describing: Bundle.main.bundleIdentifier!.description))")
            sentLogEvent("vguard: \(String(describing: vGuardManager.sdkVersion().description))")
            sentLogEvent("vos processor: \(String(describing: VosWrapper.getProcessorVersion().description))")
            sentLogEvent("firmware: \(String(describing: VosWrapper.getFirmwareVersion().description))")
            
            // NOTE: un-comment code below if your app don't use SSL pining feature | default is enable
                        vGuardManager.allowsArbitraryNetworking(true)
        } else {
            if let error = error {
                let nsError = error as NSError
                
                if (nsError.code == -8 || nsError.code == -5 || nsError.code == -3) {
                    sentLogEventError("trusted storage error: \(nsError.code)")
                    // Only trigger reset logic 1 times per app launch
                    if resetAttemptCount < 1 {
                        resetAttemptCount += 1
                        sentLogEventWarning("Triggering reset attempt \(resetAttemptCount)")
                        
                        // trigger reset API
                        vGuardManager.resetVOSTrustedStorage()
                        vGuardManager = nil
                        
                        // re-init and re-start vguard under this
                        self.VkeyConfiguration()
                        self.trigerStartScan()
                    } else {
                        sentLogEvent("Reset attempt limit reached (1). Skipping reset.")
                    }
                } else if (nsError.code == -1039 || nsError.code == 20050) {
                    // NOTE: show alert and quit app in case simulator detected
                } else if (nsError.code < -999) {// case -1005, -1006, -1007, -1008
                    // NOTE: show alert and quit app in other error
                    
                    // NOTE: un-comment code below if your app use TLA feature -> sent error log to dashboard
//                    sentLogEvent("forceSyncLog triggered")
//                    VosWrapper.forceSyncLogs()
                }
            }
        }
    }
    
    func statusVGuard(_ status: VGUARD_STATUS, withError error: Error?) {
        if ( status == VGUARD_SAFE) {
            sentLogEvent("statusVGuard: VGUARD_SAFE")
        } else {
            sentLogEventWarning("statusVGuard: VGUARD_UNSAFE - \(status.rawValue) :error \(String(describing: error))")
        }
    }
    
    func vGuardDidFinishInitializing(_ status: Bool, withError error: Error?) {
        sentLogEvent("vGuardDidFinishInitializing")
        
//        // Calculate and log performance time
//        if let startTime = vGuardScanStartTime {
//            let endTime = CFAbsoluteTimeGetCurrent()
//            let elapsedTime = endTime - startTime
//            let elapsedTimeMs = elapsedTime * 1000 // Convert to milliseconds
//            sentLogEventSuccess("⏱️ vGuard init completed in \(String(format: "%.2f", elapsedTimeMs))ms (\(String(format: "%.3f", elapsedTime))s)")
//
//            // Reset the start time for next measurement
//            vGuardScanStartTime = nil
//        }
            
        if let error = error {
            let nsError = error as NSError
            if (nsError.code >= 20000 && nsError.code < 30000 && nsError.code != 20035) {// ignore 20035 since it was handled in statusVOS callback (-8)
                // NOTE: should show alert app in not safe environment and quit app base on your business logic
                
                // NOTE: un-comment code below if your app use TLA feature -> sent error log to dashboard
                sentLogEvent("forceSyncLog triggered")
                VosWrapper.forceSyncLogs()
            }
        }
    }
    
    func vGuardDidDetectSSLError(_ error: Error) {
        sentLogEventWarning("vGuardDidDetectSSLError:\(String(describing: error))")
    }
    
    // MARK: VGuardThreatsDelegate
    func vGuardScan(_ threatsArray: [Any]!) {
        // Calculate and log performance time
        if let startTime = vGuardScanStartTime {
            let endTime = CFAbsoluteTimeGetCurrent()
            let elapsedTime = endTime - startTime
            let elapsedTimeMs = elapsedTime * 1000 // Convert to milliseconds

//            logManager.logPerformance("⏱️ vGuard scan completed in \(String(format: "%.2f", elapsedTimeMs))ms (\(String(format: "%.3f", elapsedTime))s)")
            sentLogEventSuccess("⏱️ vGuard scan completed in \(String(format: "%.2f", elapsedTimeMs))ms (\(String(format: "%.3f", elapsedTime))s)")

            // Reset the start time for next measurement
            vGuardScanStartTime = nil
        } else {
            logManager.logWarning("⚠️ vGuard scan completed but no start time recorded")
        }

        //NOTE: handle threats found here base on your business logic
        if ((threatsArray).count > 0) {
            sentLogEventWarning("vGuardScan callback threats:\n\(String(describing: threatsArray))")
        } else {
            sentLogEventSuccess("vGuardScan callback no threats")
        }
    }
    
    func vGuardDidDetectThreats(_ threatsInfo: [AnyHashable : Any]!) {
        sentLogEvent("vGuardDidDetectThreats")
    }
    
    func vGuardDidDetectScreenSharing() {
        sentLogEventWarning("vGuardDidDetectScreenSharing yes ***")
    }
    
    // MARK: VGuardExceptionHandlerProtocol
    func vGuardExceptionHandler(_ exception: NSException!) {
        sentLogEventWarning("vGuardExceptionHandler: \(String(describing: exception))")
    }
}

// copy asset

@available(iOS 13.4, *)
    func checkVKeyAssetsAndReplace() {
        let fileNames = ["firmware", "signature", "vkeylicensepack", "profile", "voscodesign.vky"]
        guard let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.path else {
            print("^Error: Unable to access documents directory")
            return
        }

        for fileName in fileNames {
            let docFilePath = (documentsPath as NSString).appendingPathComponent(fileName)
            let bundleFilePath = Bundle.main.path(forResource: fileName, ofType: nil)
            
            let docSha256 = sha256HashOfFile(atPath: docFilePath)
            let bundleSha256 = sha256HashOfFile(atPath: bundleFilePath ?? "")
            
            print("^Checking file: \(fileName)")
            print("^Document SHA-256: \(docSha256 ?? "nil")")
            print("^Bundle SHA-256:   \(bundleSha256 ?? "nil")")
            
            if let docSha256 = docSha256, let bundleSha256 = bundleSha256, docSha256 != bundleSha256, let bundleFilePath = bundleFilePath {
                do {
                    if FileManager.default.fileExists(atPath: docFilePath) {
                        try FileManager.default.removeItem(atPath: docFilePath)
                    }
                    try FileManager.default.copyItem(atPath: bundleFilePath, toPath: docFilePath)
                    print("^replace: \(fileName)")
                } catch {
                    print("^Error replacing file \(fileName): \(error)")
                }
            }
        }
    }

    private func sha256HashOfFile(atPath path: String) -> String? {
        guard let fileHandle = FileHandle(forReadingAtPath: path) else { return nil }
        defer { fileHandle.closeFile() }
        
        var context = CC_SHA256_CTX()
        CC_SHA256_Init(&context)
        
        while autoreleasepool(invoking: {
            let data = fileHandle.readData(ofLength: 1024 * 1024)
            if !data.isEmpty {
                data.withUnsafeBytes { buffer in
                    _ = CC_SHA256_Update(&context, buffer.baseAddress, CC_LONG(buffer.count))
                }
                return true
            }
            return false
        }) {}
        
        var digest = [UInt8](repeating: 0, count: Int(CC_SHA256_DIGEST_LENGTH))
        CC_SHA256_Final(&digest, &context)
        
        return digest.map { String(format: "%02hhx", $0) }.joined()
    }

