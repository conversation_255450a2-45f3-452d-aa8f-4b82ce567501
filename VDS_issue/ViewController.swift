//
//  ViewController.swift
//  sample_app_tudo
//
//  Created by <PERSON> <PERSON> on 27/09/2022.
//

import UIKit
import Foundation

class ViewController: UIViewController {
    // take screenSize to user later
    let screenSize: CGRect = UIScreen.main.bounds
    
    var logView: UITextView?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setBackground()
        setupLogUI()
//        addSecureTextField()
        
        setupButtons()
        
        // make your view controller listen to your new notification
        NotificationCenter.default.addObserver(self, selector: #selector(updateLabel), name: .textUpdatedNotification, object: nil)
    }
    
    @objc func triggerForceSyncLog(sender: UIButton!) {
        self.appDelegate.sentLogEvent("forceSyncLog triggered")
        VosWrapper.forceSyncLogs()
    }
    
    @objc func clearAction(sender: UIButton!) {
        appDelegate.clearUILog()
    }
    
    @objc func simulateError8(sender: UIButton!) {
        appDelegate.simulate8iOS()
    }
    
    @objc func startVguardAction(sender: UIButton!) {
        appDelegate.trigerStartScan()
    }
    @objc func getTroubleshootingIDAction(sender: UIButton!) {
        appDelegate.getTroubleshootingID()
    }
    
    func addSecureTextField() {
        self.prepareSecureKeyBoard()
        
        let txtField = UITextField(frame: CGRect(x: 5, y: 40, width: screenSize.width-10, height: 40))
        txtField.keyboardType = .numberPad
        txtField.backgroundColor = UIColor(red: 0.8, green: 0.6, blue: 0.8, alpha: 1.0)
        txtField.textColor = .white
        txtField.layer.cornerRadius = 5
        txtField.placeholder = "... Secure keyboard"
        self.view.addSubview(txtField)
        txtField.becomeFirstResponder()
    }
    

    
    
    func setBackground() {
        if let image = UIImage(named: "bgw") {
            view.backgroundColor = UIColor(patternImage: image)
        }
    }
    
    func setupButtons() {
        // 🎯 EASY BUTTON CONFIGURATION - Human-Friendly!
        // ✅ To add a button: Add a new line with (title: "Button Name", action: #selector(methodName(sender:)))
        // ❌ To remove a button: Delete or comment out the line
        // ✏️ To change button text: Just change the title string
        // 🔄 To reorder buttons: Move lines up or down
        // 🎨 Colors: Automatically random (handled by VKeyExtension)

        let buttonConfigs = [
            (title: "start VGuard", action: #selector(startVguardAction(sender:))),
            (title: "troubleshooting ID", action: #selector(getTroubleshootingIDAction(sender:))),
            (title: "simulate error -8", action: #selector(simulateError8(sender:))),
            (title: "get vos status", action: #selector(exec_VOS(sender:))),
            (title: "force sync log", action: #selector(triggerForceSyncLog(sender:))),
            (title: "clearUILog", action: #selector(clearAction(sender:))),
            (title: "Test Log Styles", action: #selector(testLogStyles(sender:))),
            (title: "Test VKeyExtension", action: #selector(testVKeyExtension(sender:))),

            // 💡 EXAMPLES: Uncomment any of these to add more buttons:
            // (title: "Switch to VLogView", action: #selector(switchToVLogView(sender:))),
            // (title: "Custom Action", action: #selector(customAction(sender:))),
            // (title: "Debug Info", action: #selector(showDebugInfo(sender:))),
        ]

        // ⚙️ Setup buttons using VKeyExtension (no need to modify this part)
        setupButtonsFromConfig(buttonConfigs)
    }

    // Helper method to handle the technical details
    private func setupButtonsFromConfig(_ configs: [(title: String, action: Selector)]) {
        let buttonTitles = configs.map { $0.title }
        let buttonSelectors = configs.map { $0.action }
        let selectorValues = buttonSelectors.map { NSValue(pointer: unsafeBitCast($0, to: UnsafeRawPointer.self)) }

        _ = VKeyExtension.sharedManager().setupButtons(in: self.view,
                                               target: self,
                                               buttonTitles: buttonTitles,
                                               buttonColors: nil, // nil = random colors
                                               buttonSelectors: selectorValues)
    }

    // Legacy method for backward compatibility - now delegates to VKeyExtension
    func addButtons(numberOfButtons: Int, buttonDetails: [(title: String, backgroundColor: UIColor, action: Selector)]) {
        // Convert to arrays for VKeyExtension
        var buttonTitles: [String] = []
        var buttonColors: [UIColor] = []
        var buttonActions: [String] = []

        for detail in buttonDetails {
            buttonTitles.append(detail.title)
            buttonColors.append(detail.backgroundColor)
            buttonActions.append(NSStringFromSelector(detail.action))
        }

        // Use VKeyExtension string selector method for backward compatibility
        _ = VKeyExtension.sharedManager().setupButtonsInView(withStringSelectors: self.view,
                                                     target: self,
                                                     buttonTitles: buttonTitles,
                                                     buttonColors: buttonColors,
                                                     buttonActions: buttonActions)
    }
    
    func setupLogUI() {
        // Use VKeyExtension to setup the log UI
        if logView == nil {
            logView = VKeyExtension.sharedManager().setupLogUI(in: self.view)
        }
    }

    // Legacy method for backward compatibility - now delegates to VKeyExtension
    func addLogUI(logContent: String,
                  textColor: UIColor = .black,
                  backgroundColor: UIColor = .clear,
                  fontSize: CGFloat = 14,
                  isBold: Bool = false,
                  isItalic: Bool = false,
                  attributedText: NSAttributedString? = nil) {

        // Ensure logView is setup
        if logView == nil {
            setupLogUI()
        }

        // Use VKeyExtension to update the UI
        if let logView = logView {
            VKeyExtension.sharedManager().updateLogUI(logView,
                                              logContent: logContent,
                                              textColor: textColor,
                                              backgroundColor: backgroundColor,
                                              fontSize: fontSize,
                                              isBold: isBold,
                                              isItalic: isItalic,
                                              attributedText: attributedText)
        }
    }


    // function that gets called when a notification is received
    @objc func updateLabel(_ notification: Notification) {
        // Ensure logView is setup
        if logView == nil {
            setupLogUI()
        }

        // Use VKeyExtension to update the UI with accumulated logs
        if let logView = logView {
            VKeyExtension.sharedManager().updateLogUI(withAccumulatedLogs: logView)
        }
    }
    
    func prepareSecureKeyBoard() {
        // config secure keyboard
        let secureKeyboard = VKeySecureKeypad.sharedModule()
        secureKeyboard?.enableScrambleKeypad = false
        secureKeyboard?.enableKeyboard = true
        secureKeyboard?.keypadButtonBackgroundColor = UIColor(red: 0.8, green: 0.6, blue: 0.8, alpha: 1.0)
        secureKeyboard?.keypadCloseButtonText = "vkey custom"
    }
    
    @objc func exec_VOS(sender: UIButton!) {
        let execRet = VosWrapper.execute{}
        self.appDelegate.sentLogEvent("### execRet ## rs:\n \(String(describing: execRet))")
    }

    @objc func testLogStyles(sender: UIButton!) {
        // Test different log styles using AppDelegate convenience methods
        appDelegate.sentLogEventSuccess("✅ This is a SUCCESS message")
        appDelegate.sentLogEventError("❌ This is an ERROR message")
        appDelegate.sentLogEventWarning("⚠️ This is a WARNING message")
        appDelegate.sentLogEventInfo("ℹ️ This is an INFO message")
        appDelegate.sentLogEventDebug("🐛 This is a DEBUG message")
        appDelegate.sentLogEventHighlight("🔥 This is a HIGHLIGHTED message")

        // Test custom formatting
        appDelegate.sentLogEvent("🎨 Custom formatted text",
                                textColor: .systemPurple,
                                backgroundColor: .systemGray6,
                                fontSize: 16,
                                isBold: true,
                                isItalic: true)
    }

    @objc func testVKeyExtension(sender: UIButton!) {
        let keyExtension = VKeyExtension.sharedManager()

        // Test all convenience methods
        keyExtension.logSuccess("VKeyExtension Success Test")
        keyExtension.logError("VKeyExtension Error Test")
        keyExtension.logWarning("VKeyExtension Warning Test")
        keyExtension.logInfo("VKeyExtension Info Test")
        keyExtension.logDebug("VKeyExtension Debug Test")
        keyExtension.logHighlight("VKeyExtension Highlight Test")

        // Test custom formatting using legacy method
        keyExtension.sentLogEvent("Custom purple text",
                                 textColor: .systemPurple,
                                 backgroundColor: .systemGray6,
                                 fontSize: 16,
                                 isBold: true,
                                 isItalic: false)

        // Test additional messages
        keyExtension.sentLogEvent("VKeyExtension is working perfectly!")
        keyExtension.sentLogEventSuccess("All tests completed successfully!")
    }
}

extension UIViewController {
    var appDelegate: AppDelegate {
        return UIApplication.shared.delegate as! AppDelegate
    }
}
extension Notification.Name {
    static let textUpdatedNotification = Notification.Name("textUpdatedNotification")
}
