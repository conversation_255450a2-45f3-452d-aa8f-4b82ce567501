//
//  VKeyExtension.h
//  V_app_swift
//
//  Created by Assistant on 2025-07-09.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 * Log level enumeration for different types of log messages
 */
typedef NS_ENUM(NSInteger, VLogLevel) {
    VLogLevelDefault = 0,
    VLogLevelSuccess,
    VLogLevelError,
    VLogLevelWarning,
    VLogLevelInfo,
    VLogLevelDebug,
    VLogLevelHighlight
};

/**
 * VKeyExtension - A comprehensive logging and UI extension module for iOS applications
 * Provides formatted logging with colors, fonts, text decoration, and button management
 */
@interface VKeyExtension : NSObject

/**
 * Shared singleton instance
 */
+ (instancetype)sharedManager;

/**
 * Main logging method with full customization options
 * @param logText The text to log
 * @param textColor Color of the text (default: black)
 * @param backgroundColor Background color of the text (default: clear)
 * @param fontSize Font size (default: 14)
 * @param isBold Whether text should be bold (default: NO)
 * @param isItalic Whether text should be italic (default: NO)
 */
- (void)logText:(NSString *)logText
      textColor:(UIColor * _Nullable)textColor
backgroundColor:(UIColor * _Nullable)backgroundColor
       fontSize:(CGFloat)fontSize
         isBold:(BOOL)isBold
       isItalic:(BOOL)isItalic;

/**
 * Simplified logging method with default formatting
 * @param logText The text to log
 */
- (void)logText:(NSString *)logText;

/**
 * Convenience methods for different log levels
 */
- (void)logSuccess:(NSString *)logText;
- (void)logError:(NSString *)logText;
- (void)logWarning:(NSString *)logText;
- (void)logInfo:(NSString *)logText;
- (void)logDebug:(NSString *)logText;
- (void)logHighlight:(NSString *)logText;

/**
 * Log with predefined level styling
 * @param logText The text to log
 * @param level The log level which determines styling
 */
- (void)logText:(NSString *)logText withLevel:(VLogLevel)level;

/**
 * Clear all accumulated logs
 */
- (void)clearLogs;

/**
 * Get the current plain text log content
 */
- (NSString *)getPlainTextLog;

/**
 * Get the current attributed text log content
 */
- (NSAttributedString *)getAttributedTextLog;

/**
 * Enable/disable console logging (NSLog)
 * @param enabled YES to enable console logging, NO to disable
 */
- (void)setConsoleLoggingEnabled:(BOOL)enabled;

/**
 * Set custom notification name for log updates
 * @param notificationName Custom notification name
 */
- (void)setLogUpdateNotificationName:(NSString *)notificationName;

/**
 * Get the current log update notification name
 */
- (NSString *)getLogUpdateNotificationName;

#pragma mark - Legacy AppDelegate Methods (for easy migration)

/**
 * Legacy methods that match AppDelegate's logging signatures
 * These allow easy migration from AppDelegate to VLogManager
 */
- (void)sentLogEvent:(NSString *)logText
           textColor:(UIColor * _Nullable)textColor
     backgroundColor:(UIColor * _Nullable)backgroundColor
            fontSize:(CGFloat)fontSize
              isBold:(BOOL)isBold
            isItalic:(BOOL)isItalic;

- (void)sentLogEvent:(NSString *)logText;
- (void)sentLogEventSuccess:(NSString *)logText;
- (void)sentLogEventError:(NSString *)logText;
- (void)sentLogEventWarning:(NSString *)logText;
- (void)sentLogEventInfo:(NSString *)logText;
- (void)sentLogEventDebug:(NSString *)logText;
- (void)sentLogEventHighlight:(NSString *)logText;
- (void)clearUILog;

#pragma mark - Swift Compatibility

/**
 * Setup method to configure VLogManager for legacy notification support
 * Call this in AppDelegate to maintain compatibility with existing UI
 */
- (void)setupLegacyNotificationSupport;

#pragma mark - UI Management

/**
 * Setup and configure a log UI view for a given view controller
 * @param parentView The parent view to add the log UI to
 * @param logContent Initial log content (pass empty string to show accumulated logs)
 * @param textColor Text color for the log content
 * @param backgroundColor Background color for the log content
 * @param fontSize Font size for the log content
 * @param isBold Whether the text should be bold
 * @param isItalic Whether the text should be italic
 * @param attributedText Optional attributed text to display
 * @return The created UITextView for the log display
 */
- (UITextView *)setupLogUIInView:(UIView *)parentView
                      logContent:(NSString *)logContent
                       textColor:(UIColor *)textColor
                 backgroundColor:(UIColor *)backgroundColor
                        fontSize:(CGFloat)fontSize
                          isBold:(BOOL)isBold
                        isItalic:(BOOL)isItalic
                  attributedText:(NSAttributedString * _Nullable)attributedText;

/**
 * Convenience method to setup log UI with default parameters
 * @param parentView The parent view to add the log UI to
 * @return The created UITextView for the log display
 */
- (UITextView *)setupLogUIInView:(UIView *)parentView;

/**
 * Update an existing log UI view with new content
 * @param logView The UITextView to update
 * @param logContent Log content to add (pass empty string to show accumulated logs)
 * @param textColor Text color for the log content
 * @param backgroundColor Background color for the log content
 * @param fontSize Font size for the log content
 * @param isBold Whether the text should be bold
 * @param isItalic Whether the text should be italic
 * @param attributedText Optional attributed text to display
 */
- (void)updateLogUI:(UITextView *)logView
         logContent:(NSString *)logContent
          textColor:(UIColor *)textColor
    backgroundColor:(UIColor *)backgroundColor
           fontSize:(CGFloat)fontSize
             isBold:(BOOL)isBold
           isItalic:(BOOL)isItalic
     attributedText:(NSAttributedString * _Nullable)attributedText;

/**
 * Convenience method to update log UI with accumulated logs
 * @param logView The UITextView to update
 */
- (void)updateLogUIWithAccumulatedLogs:(UITextView *)logView;

#pragma mark - Button Management

/**
 * Setup and configure buttons in a scrollable view for a given view controller
 * @param parentView The parent view to add the button scroll view to
 * @param target The target object for button actions (usually the view controller)
 * @param buttonTitles Array of NSString titles for buttons
 * @param buttonColors Array of UIColor for button backgrounds (pass nil for random colors)
 * @param buttonSelectors Array of SEL selectors for button actions
 * @param topMargin Top margin for the scroll view (default: 120)
 * @param heightRatio Height ratio of parent view for scroll view (default: 0.5)
 * @param heightOffset Height offset to subtract from calculated height (default: 140)
 * @return The created UIScrollView containing the buttons
 */
- (UIScrollView *)setupButtonsInView:(UIView *)parentView
                              target:(id)target
                        buttonTitles:(NSArray<NSString *> *)buttonTitles
                        buttonColors:(NSArray<UIColor *> * _Nullable)buttonColors
                     buttonSelectors:(NSArray<NSValue *> *)buttonSelectors
                           topMargin:(CGFloat)topMargin
                         heightRatio:(CGFloat)heightRatio
                        heightOffset:(CGFloat)heightOffset;

/**
 * Convenience method to setup buttons with default layout parameters
 * @param parentView The parent view to add the button scroll view to
 * @param target The target object for button actions
 * @param buttonTitles Array of NSString titles for buttons
 * @param buttonColors Array of UIColor for button backgrounds (pass nil for random colors)
 * @param buttonSelectors Array of SEL selectors for button actions
 * @return The created UIScrollView containing the buttons
 */
- (UIScrollView *)setupButtonsInView:(UIView *)parentView
                              target:(id)target
                        buttonTitles:(NSArray<NSString *> *)buttonTitles
                        buttonColors:(NSArray<UIColor *> * _Nullable)buttonColors
                     buttonSelectors:(NSArray<NSValue *> *)buttonSelectors;

/**
 * Legacy method - Setup buttons using string selector names (for backward compatibility)
 * @param parentView The parent view to add the button scroll view to
 * @param target The target object for button actions
 * @param buttonTitles Array of NSString titles for buttons
 * @param buttonColors Array of UIColor for button backgrounds (pass nil for random colors)
 * @param buttonActions Array of NSString selector names for button actions
 * @return The created UIScrollView containing the buttons
 */
- (UIScrollView *)setupButtonsInViewWithStringSelectors:(UIView *)parentView
                                                 target:(id)target
                                           buttonTitles:(NSArray<NSString *> *)buttonTitles
                                           buttonColors:(NSArray<UIColor *> * _Nullable)buttonColors
                                          buttonActions:(NSArray<NSString *> *)buttonActions;

/**
 * Generate a random color for button backgrounds
 * @return A random UIColor
 */
- (UIColor *)randomColor;

@end

NS_ASSUME_NONNULL_END
