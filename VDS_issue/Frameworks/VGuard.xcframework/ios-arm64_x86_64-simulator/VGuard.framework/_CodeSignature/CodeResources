<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>Headers/VGuard-Swift.h</key>
		<data>
		ospQqtertB8x3u4IoFLO3MibNHE=
		</data>
		<key>Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>Headers/VGuardManager.h</key>
		<data>
		H0coipAoUJoXGsPDSioqJIcIQfY=
		</data>
		<key>Headers/VGuardThreats.h</key>
		<data>
		CDZGRDZuMS78xqyMrkdwitrnm8M=
		</data>
		<key>Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>IPS/VGuardDetectionManager.h</key>
		<data>
		51zmKixeJCA2EXyytbh+qBlU+7g=
		</data>
		<key>IPS/VGuardDetectionManager.m</key>
		<data>
		SE8x/OoInHPM0Y2ZPXsNzYiSY84=
		</data>
		<key>IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>Info.plist</key>
		<data>
		/66AqPaDAfr4bBVRhFPGR9Mxxnk=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		tfN11YPD+r75K/S3FnU6w34JtiM=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		zP4/zr3D09PKspKAJdzgdpiIVuk=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		tfN11YPD+r75K/S3FnU6w34JtiM=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<data>
		s5mGNRgCVzMXpw/IRwrXWBmKTBY=
		</data>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		pl12kK57xnpbgdM8/Dfygk3fiCk=
		</data>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		EFmglgeL94Qrj9qEaKHsmqUb1Jw=
		</data>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		pl12kK57xnpbgdM8/Dfygk3fiCk=
		</data>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		utmJ5NQORb2vuLNHZitWMH3NgRw=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		E4/CbwB3LVIhIc0LUz7RYzd8As8=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>VGuard_Info.plist</key>
		<data>
		ak23OVbs/BvdmNUe1iiIBBpIROU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>Headers/VGuard-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			ospQqtertB8x3u4IoFLO3MibNHE=
			</data>
			<key>hash2</key>
			<data>
			4WkEnJ7ZBXl1M6ybiDRmguIHEPF3QIVSD5kPV+Hi3o0=
			</data>
		</dict>
		<key>Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			H0coipAoUJoXGsPDSioqJIcIQfY=
			</data>
			<key>hash2</key>
			<data>
			8c3JAYdKu6Fx2vz8GiDp/EAAYy9SqXpv7EtYDCHPtBw=
			</data>
		</dict>
		<key>Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			CDZGRDZuMS78xqyMrkdwitrnm8M=
			</data>
			<key>hash2</key>
			<data>
			9Xsc7OaKNq0VV7ja6Z5xvBfu9zgrrZKTYWiwshl9G3Q=
			</data>
		</dict>
		<key>Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			51zmKixeJCA2EXyytbh+qBlU+7g=
			</data>
			<key>hash2</key>
			<data>
			MxbxqvzedaXsYvZ58X+98j2eeIsvReGI7lHhhR6IfA8=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			SE8x/OoInHPM0Y2ZPXsNzYiSY84=
			</data>
			<key>hash2</key>
			<data>
			Dz0Y9b2KVwZGv3gSVe3qZnIu7QRAVxHd0S7acl4xOc4=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tfN11YPD+r75K/S3FnU6w34JtiM=
			</data>
			<key>hash2</key>
			<data>
			j33VYqUyv38X7j+5SQTfvWXhu/otxMFgzb1noN0wfwg=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			zP4/zr3D09PKspKAJdzgdpiIVuk=
			</data>
			<key>hash2</key>
			<data>
			7Gucawi8FpoP0Eu3WiWst8zMAFncCLxrlzvqmQrBHBE=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			tfN11YPD+r75K/S3FnU6w34JtiM=
			</data>
			<key>hash2</key>
			<data>
			j33VYqUyv38X7j+5SQTfvWXhu/otxMFgzb1noN0wfwg=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			s5mGNRgCVzMXpw/IRwrXWBmKTBY=
			</data>
			<key>hash2</key>
			<data>
			dOP5hk7QtsG/AmqA9brtsg6hN/42Azht5EmXRUwON04=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			pl12kK57xnpbgdM8/Dfygk3fiCk=
			</data>
			<key>hash2</key>
			<data>
			0fm+LEFH5PRI/Kuvtw8gep8qBQlSTAqU8sf/MDQpRbo=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			EFmglgeL94Qrj9qEaKHsmqUb1Jw=
			</data>
			<key>hash2</key>
			<data>
			HoN6vX8l+TTmBl6E0pF8stGq6ikNo1jDVnhjQ+NOImE=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			pl12kK57xnpbgdM8/Dfygk3fiCk=
			</data>
			<key>hash2</key>
			<data>
			0fm+LEFH5PRI/Kuvtw8gep8qBQlSTAqU8sf/MDQpRbo=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			utmJ5NQORb2vuLNHZitWMH3NgRw=
			</data>
			<key>hash2</key>
			<data>
			ssnaWNGxacLHhPb5Q0YTQqxl7sQMLiuMwyo8LS0zz5k=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			E4/CbwB3LVIhIc0LUz7RYzd8As8=
			</data>
			<key>hash2</key>
			<data>
			0pMFMn2XVP5MKgrbUPaEqJaaPrC63D+XUW4UtHlID6A=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ak23OVbs/BvdmNUe1iiIBBpIROU=
			</data>
			<key>hash2</key>
			<data>
			4sDUzmZGhzYFUYQNbDWIQNXc+UBkqmzww2h903giT64=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
