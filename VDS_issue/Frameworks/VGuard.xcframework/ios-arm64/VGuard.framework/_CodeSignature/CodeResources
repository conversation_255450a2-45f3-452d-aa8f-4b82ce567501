<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<data>
		pdS8MqGbpNRaANrUCsfwsglnAjo=
		</data>
		<key>Headers/VGConstant.h</key>
		<data>
		U/dYFqOs/VHXlZGJFDvjw/ETzgM=
		</data>
		<key>Headers/VGuard-Swift.h</key>
		<data>
		OYp+k432cGOPdFc7rWW0Gz+maSM=
		</data>
		<key>Headers/VGuard.h</key>
		<data>
		fJKMYoAVCj/4PVATtYlhMve/TjY=
		</data>
		<key>Headers/VGuardExceptionHandler.h</key>
		<data>
		STogGBI2fnUrsMXzOgvI0Cnfiq8=
		</data>
		<key>Headers/VGuardManager.h</key>
		<data>
		H0coipAoUJoXGsPDSioqJIcIQfY=
		</data>
		<key>Headers/VGuardThreats.h</key>
		<data>
		CDZGRDZuMS78xqyMrkdwitrnm8M=
		</data>
		<key>Headers/VKeySecureKeypad.h</key>
		<data>
		DbgX6q+h4AgpO3o56nLei6KcqRY=
		</data>
		<key>IPS/VGuardDetectionManager.h</key>
		<data>
		51zmKixeJCA2EXyytbh+qBlU+7g=
		</data>
		<key>IPS/VGuardDetectionManager.m</key>
		<data>
		SE8x/OoInHPM0Y2ZPXsNzYiSY84=
		</data>
		<key>IPS/VGuardIPSModule.h</key>
		<data>
		A4bzgsShEYEq7Q/nK8xJjg7/nCk=
		</data>
		<key>IPS/VGuardIPSModule.m</key>
		<data>
		iwIgbM1R2r0RWmYzlNBbl10qHQc=
		</data>
		<key>Info.plist</key>
		<data>
		9IINUurKdNUyjou5I2MEnFzkoL0=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		7tgYOPEUxMBF2bPvXsfRGFAq1B0=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		deygybyRFZyPLessWeVWafBzlRA=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
		</data>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<data>
		u/8cKxvRCvUEUPRgRDvTLF2oceU=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		E4/CbwB3LVIhIc0LUz7RYzd8As8=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		doi9LJCWvrold7arjSlypDp7efw=
		</data>
		<key>VGuard_Info.plist</key>
		<data>
		ak23OVbs/BvdmNUe1iiIBBpIROU=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/NSUserDefaults.h</key>
		<dict>
			<key>hash</key>
			<data>
			pdS8MqGbpNRaANrUCsfwsglnAjo=
			</data>
			<key>hash2</key>
			<data>
			Tc5LZbctIp3Y56SCDHmsbc1UwN6Nu04qyPzKAzhUkmU=
			</data>
		</dict>
		<key>Headers/VGConstant.h</key>
		<dict>
			<key>hash</key>
			<data>
			U/dYFqOs/VHXlZGJFDvjw/ETzgM=
			</data>
			<key>hash2</key>
			<data>
			w7rOuqpKuxO9t2spl7TKgo8kvnX1/70vwolHd1fWyrA=
			</data>
		</dict>
		<key>Headers/VGuard-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			OYp+k432cGOPdFc7rWW0Gz+maSM=
			</data>
			<key>hash2</key>
			<data>
			0eRr8+r8FX8kkG/w9kvCygy2ikxjXRUSdIA0w1vUhHw=
			</data>
		</dict>
		<key>Headers/VGuard.h</key>
		<dict>
			<key>hash</key>
			<data>
			fJKMYoAVCj/4PVATtYlhMve/TjY=
			</data>
			<key>hash2</key>
			<data>
			kNXtsDiFrkfbUDAQVhJ7dBUovbmU7Pszt1iS9485MWA=
			</data>
		</dict>
		<key>Headers/VGuardExceptionHandler.h</key>
		<dict>
			<key>hash</key>
			<data>
			STogGBI2fnUrsMXzOgvI0Cnfiq8=
			</data>
			<key>hash2</key>
			<data>
			uk29EmgH2VYrMZLxlaem6mmS8WqYvxB75cL+o0A0MVM=
			</data>
		</dict>
		<key>Headers/VGuardManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			H0coipAoUJoXGsPDSioqJIcIQfY=
			</data>
			<key>hash2</key>
			<data>
			8c3JAYdKu6Fx2vz8GiDp/EAAYy9SqXpv7EtYDCHPtBw=
			</data>
		</dict>
		<key>Headers/VGuardThreats.h</key>
		<dict>
			<key>hash</key>
			<data>
			CDZGRDZuMS78xqyMrkdwitrnm8M=
			</data>
			<key>hash2</key>
			<data>
			9Xsc7OaKNq0VV7ja6Z5xvBfu9zgrrZKTYWiwshl9G3Q=
			</data>
		</dict>
		<key>Headers/VKeySecureKeypad.h</key>
		<dict>
			<key>hash</key>
			<data>
			DbgX6q+h4AgpO3o56nLei6KcqRY=
			</data>
			<key>hash2</key>
			<data>
			tBdvLT4LL0GwARbUefgcVAh8ByIEtYwOEjWMZtE6fkM=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.h</key>
		<dict>
			<key>hash</key>
			<data>
			51zmKixeJCA2EXyytbh+qBlU+7g=
			</data>
			<key>hash2</key>
			<data>
			MxbxqvzedaXsYvZ58X+98j2eeIsvReGI7lHhhR6IfA8=
			</data>
		</dict>
		<key>IPS/VGuardDetectionManager.m</key>
		<dict>
			<key>hash</key>
			<data>
			SE8x/OoInHPM0Y2ZPXsNzYiSY84=
			</data>
			<key>hash2</key>
			<data>
			Dz0Y9b2KVwZGv3gSVe3qZnIu7QRAVxHd0S7acl4xOc4=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.h</key>
		<dict>
			<key>hash</key>
			<data>
			A4bzgsShEYEq7Q/nK8xJjg7/nCk=
			</data>
			<key>hash2</key>
			<data>
			7KoTSTNooVBgG2ByOFu9NtotwDxRhLOFCg+JyuEmaCI=
			</data>
		</dict>
		<key>IPS/VGuardIPSModule.m</key>
		<dict>
			<key>hash</key>
			<data>
			iwIgbM1R2r0RWmYzlNBbl10qHQc=
			</data>
			<key>hash2</key>
			<data>
			CfY2pxJH+8ZI+zWDq4e5449R9hKeZjrpsEO6Jyzdbl4=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			7tgYOPEUxMBF2bPvXsfRGFAq1B0=
			</data>
			<key>hash2</key>
			<data>
			45HAOmpf0eeVrW3K8M5W3vCozD79J24opspY0ng1saI=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
			</data>
			<key>hash2</key>
			<data>
			E/4VLnY+n150X+k8vAMmzjbPqJfXRlVObvaLoERDWnM=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			deygybyRFZyPLessWeVWafBzlRA=
			</data>
			<key>hash2</key>
			<data>
			tvKRGQlwQ+nvbzzUStKu671D4+eYLLQG2yfWZU8fprU=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			7OfAVdmZhzJ9NX1TVD16b/dRW8Y=
			</data>
			<key>hash2</key>
			<data>
			E/4VLnY+n150X+k8vAMmzjbPqJfXRlVObvaLoERDWnM=
			</data>
		</dict>
		<key>Modules/VGuard.swiftmodule/arm64-apple-ios.swiftmodule</key>
		<dict>
			<key>hash</key>
			<data>
			u/8cKxvRCvUEUPRgRDvTLF2oceU=
			</data>
			<key>hash2</key>
			<data>
			+2K7Q1lt34GxFcPJsoSNGLK72cGQyGuQb+sWzlyTr98=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			E4/CbwB3LVIhIc0LUz7RYzd8As8=
			</data>
			<key>hash2</key>
			<data>
			0pMFMn2XVP5MKgrbUPaEqJaaPrC63D+XUW4UtHlID6A=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			doi9LJCWvrold7arjSlypDp7efw=
			</data>
			<key>hash2</key>
			<data>
			edvKYjWggMzYW0aTphpyuZlpVPEcq8+HdIUdCwMTIw8=
			</data>
		</dict>
		<key>VGuard_Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			ak23OVbs/BvdmNUe1iiIBBpIROU=
			</data>
			<key>hash2</key>
			<data>
			4sDUzmZGhzYFUYQNbDWIQNXc+UBkqmzww2h903giT64=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
