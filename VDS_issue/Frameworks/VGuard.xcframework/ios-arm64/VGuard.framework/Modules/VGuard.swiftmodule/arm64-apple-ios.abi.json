{"ABIRoot": {"kind": "Root", "name": "TopLevel", "printedName": "TopLevel", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "VGuard", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Network", "printedName": "Network", "declKind": "Import", "moduleName": "VGuard"}, {"kind": "TypeDecl", "name": "VGuardNetworkDetectorDelegate", "printedName": "VGuardNetworkDetectorDelegate", "children": [{"kind": "Function", "name": "networkDetected", "printedName": "networkDetected(_:)", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Array", "printedName": "[Foundation.NSNumber]", "children": [{"kind": "TypeNominal", "name": "NSNumber", "printedName": "Foundation.NSNumber", "usr": "c:objc(cs)NSNumber"}], "usr": "s:Sa"}], "declKind": "Func", "usr": "c:@M@VGuard@objc(pl)VGuardNetworkDetectorDelegate(im)networkDetected:", "mangledName": "$s6VGuard0A23NetworkDetectorDelegateP15networkDetectedyySaySo8NSNumberCGF", "moduleName": "VGuard", "genericSig": "<τ_0_0 where τ_0_0 : VGuard.VGuardNetworkDetectorDelegate>", "sugared_genericSig": "<Self where Self : VGuard.VGuardNetworkDetectorDelegate>", "protocolReq": true, "declAttributes": ["Optional", "ObjC"], "reqNewWitnessTableEntry": true, "funcSelfKind": "NonMutating"}], "declKind": "Protocol", "usr": "c:@M@VGuard@objc(pl)VGuardNetworkDetectorDelegate", "mangledName": "$s6VGuard0A23NetworkDetectorDelegateP", "moduleName": "VGuard", "genericSig": "<τ_0_0 : AnyObject>", "sugared_genericSig": "<Self : AnyObject>", "declAttributes": ["AccessControl", "ObjC"]}, {"kind": "TypeDecl", "name": "VGuardNetworkDetector", "printedName": "VGuardNetworkDetector", "children": [{"kind": "Var", "name": "delegate", "printedName": "delegate", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any VGuard.VGuardNetworkDetectorDelegate)?", "children": [{"kind": "TypeNominal", "name": "VGuardNetworkDetectorDelegate", "printedName": "any VGuard.VGuardNetworkDetectorDelegate", "usr": "c:@M@VGuard@objc(pl)VGuardNetworkDetectorDelegate"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(py)delegate", "mangledName": "$s6VGuard0A15NetworkDetectorC8delegateAA0abC8Delegate_pSgvp", "moduleName": "VGuard", "isOpen": true, "declAttributes": ["HasInitialValue", "HasStorage", "AccessControl", "ObjC"], "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "(any VGuard.VGuardNetworkDetectorDelegate)?", "children": [{"kind": "TypeNominal", "name": "VGuardNetworkDetectorDelegate", "printedName": "any VGuard.VGuardNetworkDetectorDelegate", "usr": "c:@M@VGuard@objc(pl)VGuardNetworkDetectorDelegate"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(im)delegate", "mangledName": "$s6VGuard0A15NetworkDetectorC8delegateAA0abC8Delegate_pSgvg", "moduleName": "VGuard", "implicit": true, "isOpen": true, "declAttributes": ["ObjC"], "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "Optional", "printedName": "(any VGuard.VGuardNetworkDetectorDelegate)?", "children": [{"kind": "TypeNominal", "name": "VGuardNetworkDetectorDelegate", "printedName": "any VGuard.VGuardNetworkDetectorDelegate", "usr": "c:@M@VGuard@objc(pl)VGuardNetworkDetectorDelegate"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(im)setDelegate:", "mangledName": "$s6VGuard0A15NetworkDetectorC8delegateAA0abC8Delegate_pSgvs", "moduleName": "VGuard", "implicit": true, "isOpen": true, "declAttributes": ["ObjC"], "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:6VGuard0A15NetworkDetectorC8delegateAA0abC8Delegate_pSgvM", "mangledName": "$s6VGuard0A15NetworkDetectorC8delegateAA0abC8Delegate_pSgvM", "moduleName": "VGuard", "implicit": true, "isOpen": true, "intro_iOS": "12.0", "declAttributes": ["Available"], "accessorKind": "_modify"}]}, {"kind": "Function", "name": "sharedInstance", "printedName": "sharedInstance()", "children": [{"kind": "TypeNominal", "name": "VGuardNetworkDetector", "printedName": "VGuard.VGuardNetworkDetector", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector"}], "declKind": "Func", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(cm)sharedInstance", "mangledName": "$s6VGuard0A15NetworkDetectorC14sharedInstanceACyFZ", "moduleName": "VGuard", "static": true, "isOpen": true, "declAttributes": ["AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "startMonitoring", "printedName": "startMonitoring()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(im)startMonitoring", "mangledName": "$s6VGuard0A15NetworkDetectorC15startMonitoringyyF", "moduleName": "VGuard", "isOpen": true, "declAttributes": ["AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}, {"kind": "Function", "name": "stopMonitoring", "printedName": "stopMonitoring()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Func", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector(im)stopMonitoring", "mangledName": "$s6VGuard0A15NetworkDetectorC14stopMonitoringyyF", "moduleName": "VGuard", "isOpen": true, "declAttributes": ["AccessControl", "ObjC"], "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:@M@VGuard@objc(cs)VGuardNetworkDetector", "mangledName": "$s6VGuard0A15NetworkDetectorC", "moduleName": "VGuard", "isOpen": true, "intro_iOS": "12.0", "declAttributes": ["AccessControl", "ObjC", "Available"], "superclassUsr": "c:objc(cs)NSObject", "hasMissingDesignatedInitializers": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Users/<USER>/workspace/vos-app-protection-ios_master/VGuard/Private/VGuardNetworkDetector.swift", "kind": "Array", "offset": 548, "length": 4, "value": "[-1]"}]}