// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 5.10 (swiftlang-5.10.0.13 clang-1500.3.9.4)
// swift-module-flags: -target arm64-apple-ios12.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -Onone -enable-bare-slash-regex -module-name VGuard
import Foundation
import Network
import Swift
@_exported import VGuard
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
@objc public protocol VGuardNetworkDetectorDelegate {
  @objc optional func networkDetected(_ networks: Swift.Array<Foundation.NSNumber>)
}
@_inheritsConvenienceInitializers @_hasMissingDesignatedInitializers @available(iOS 12.0, *)
@objc public class VGuardNetworkDetector : ObjectiveC.NSObject {
  @objc public var delegate: (any VGuard.VGuardNetworkDetectorDelegate)?
  @objc public class func sharedInstance() -> VGuard.VGuardNetworkDetector
  @objc public func startMonitoring()
  @objc public func stopMonitoring()
  @objc deinit
}
