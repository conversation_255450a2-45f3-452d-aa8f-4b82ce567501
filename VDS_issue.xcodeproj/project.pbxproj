// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 60;
	objects = {

/* Begin PBXBuildFile section */
		8C1CFD7929261F0600C40516 /* deleteLater.m in Sources */ = {isa = PBXBuildFile; fileRef = 8C1CFD7829261F0600C40516 /* deleteLater.m */; };
		8C3DC0742E49D99900CA8907 /* firmware in Resources */ = {isa = PBXBuildFile; fileRef = 8C3DC06F2E49D99900CA8907 /* firmware */; };
		8C3DC0752E49D99900CA8907 /* profile in Resources */ = {isa = PBXBuildFile; fileRef = 8C3DC0702E49D99900CA8907 /* profile */; };
		8C3DC0762E49D99900CA8907 /* signature in Resources */ = {isa = PBXBuildFile; fileRef = 8C3DC0712E49D99900CA8907 /* signature */; };
		8C3DC0772E49D99900CA8907 /* vkeylicensepack in Resources */ = {isa = PBXBuildFile; fileRef = 8C3DC0722E49D99900CA8907 /* vkeylicensepack */; };
		8C79C2D328E2F96500DDC88B /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C79C2D228E2F96500DDC88B /* AppDelegate.swift */; };
		8C79C2D728E2F96500DDC88B /* ViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8C79C2D628E2F96500DDC88B /* ViewController.swift */; };
		8C79C2DA28E2F96500DDC88B /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8C79C2D828E2F96500DDC88B /* Main.storyboard */; };
		8C79C2DC28E2F96600DDC88B /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8C79C2DB28E2F96600DDC88B /* Assets.xcassets */; };
		8C79C2DF28E2F96600DDC88B /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 8C79C2DD28E2F96600DDC88B /* LaunchScreen.storyboard */; };
		8C79C30128E3015200DDC88B /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30028E3014A00DDC88B /* libc++.tbd */; };
		8C79C30328E3018000DDC88B /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30228E3015D00DDC88B /* libz.tbd */; };
		8C79C30528E3019C00DDC88B /* libsqlite3.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30428E3019800DDC88B /* libsqlite3.tbd */; };
		8C79C30728E301AB00DDC88B /* CoreGraphics.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30628E301AB00DDC88B /* CoreGraphics.framework */; };
		8C79C30928E301B500DDC88B /* CoreLocation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30828E301B500DDC88B /* CoreLocation.framework */; };
		8C79C30B28E301BB00DDC88B /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30A28E301BB00DDC88B /* Foundation.framework */; };
		8C79C30D28E301CA00DDC88B /* MobileCoreServices.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30C28E301CA00DDC88B /* MobileCoreServices.framework */; };
		8C79C30F28E301D500DDC88B /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C30E28E301D500DDC88B /* Security.framework */; };
		8C79C31128E301DF00DDC88B /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C31028E301DF00DDC88B /* SystemConfiguration.framework */; };
		8C79C31328E301E800DDC88B /* UIKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8C79C31228E301E800DDC88B /* UIKit.framework */; };
		8CA91EA02E49B98400035F09 /* VKeyExtension.m in Sources */ = {isa = PBXBuildFile; fileRef = 8CA91E9F2E49B98400035F09 /* VKeyExtension.m */; };
		8CFCE4602D5073D100BD5176 /* VGuard.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8CFCE45E2D5073D100BD5176 /* VGuard.xcframework */; };
		8CFCE4612D5073D100BD5176 /* VosWrapper.xcframework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8CFCE45F2D5073D100BD5176 /* VosWrapper.xcframework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		8C1CFD7729261F0600C40516 /* VDS_issue-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "VDS_issue-Bridging-Header.h"; sourceTree = "<group>"; };
		8C1CFD7829261F0600C40516 /* deleteLater.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = deleteLater.m; sourceTree = "<group>"; };
		8C3DC06F2E49D99900CA8907 /* firmware */ = {isa = PBXFileReference; lastKnownFileType = text; path = firmware; sourceTree = "<group>"; };
		8C3DC0702E49D99900CA8907 /* profile */ = {isa = PBXFileReference; lastKnownFileType = text; path = profile; sourceTree = "<group>"; };
		8C3DC0712E49D99900CA8907 /* signature */ = {isa = PBXFileReference; lastKnownFileType = text; path = signature; sourceTree = "<group>"; };
		8C3DC0722E49D99900CA8907 /* vkeylicensepack */ = {isa = PBXFileReference; lastKnownFileType = file; path = vkeylicensepack; sourceTree = "<group>"; };
		8C79C2CF28E2F96500DDC88B /* VDS_issue.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VDS_issue.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8C79C2D228E2F96500DDC88B /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		8C79C2D628E2F96500DDC88B /* ViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ViewController.swift; sourceTree = "<group>"; };
		8C79C2D928E2F96500DDC88B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		8C79C2DB28E2F96600DDC88B /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8C79C2DE28E2F96600DDC88B /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		8C79C2E028E2F96600DDC88B /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		8C79C30028E3014A00DDC88B /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		8C79C30228E3015D00DDC88B /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		8C79C30428E3019800DDC88B /* libsqlite3.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libsqlite3.tbd; path = usr/lib/libsqlite3.tbd; sourceTree = SDKROOT; };
		8C79C30628E301AB00DDC88B /* CoreGraphics.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreGraphics.framework; path = System/Library/Frameworks/CoreGraphics.framework; sourceTree = SDKROOT; };
		8C79C30828E301B500DDC88B /* CoreLocation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreLocation.framework; path = System/Library/Frameworks/CoreLocation.framework; sourceTree = SDKROOT; };
		8C79C30A28E301BB00DDC88B /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		8C79C30C28E301CA00DDC88B /* MobileCoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = MobileCoreServices.framework; path = System/Library/Frameworks/MobileCoreServices.framework; sourceTree = SDKROOT; };
		8C79C30E28E301D500DDC88B /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		8C79C31028E301DF00DDC88B /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		8C79C31228E301E800DDC88B /* UIKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = UIKit.framework; path = System/Library/Frameworks/UIKit.framework; sourceTree = SDKROOT; };
		8CA91E9E2E49B98400035F09 /* VKeyExtension.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = VKeyExtension.h; sourceTree = "<group>"; };
		8CA91E9F2E49B98400035F09 /* VKeyExtension.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = VKeyExtension.m; sourceTree = "<group>"; };
		8CFCE45E2D5073D100BD5176 /* VGuard.xcframework */ = {isa = PBXFileReference; expectedSignature = "AppleDeveloperProgram:63DA3SWC73:V-Key Pte Ltd"; lastKnownFileType = wrapper.xcframework; name = VGuard.xcframework; path = VDS_issue/Frameworks/VGuard.xcframework; sourceTree = "<group>"; };
		8CFCE45F2D5073D100BD5176 /* VosWrapper.xcframework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.xcframework; name = VosWrapper.xcframework; path = VDS_issue/Frameworks/VosWrapper.xcframework; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8C79C2CC28E2F96500DDC88B /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8C79C30B28E301BB00DDC88B /* Foundation.framework in Frameworks */,
				8C79C30528E3019C00DDC88B /* libsqlite3.tbd in Frameworks */,
				8C79C30728E301AB00DDC88B /* CoreGraphics.framework in Frameworks */,
				8CFCE4602D5073D100BD5176 /* VGuard.xcframework in Frameworks */,
				8CFCE4612D5073D100BD5176 /* VosWrapper.xcframework in Frameworks */,
				8C79C30928E301B500DDC88B /* CoreLocation.framework in Frameworks */,
				8C79C31128E301DF00DDC88B /* SystemConfiguration.framework in Frameworks */,
				8C79C30328E3018000DDC88B /* libz.tbd in Frameworks */,
				8C79C30128E3015200DDC88B /* libc++.tbd in Frameworks */,
				8C79C30F28E301D500DDC88B /* Security.framework in Frameworks */,
				8C79C30D28E301CA00DDC88B /* MobileCoreServices.framework in Frameworks */,
				8C79C31328E301E800DDC88B /* UIKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8C3DC0732E49D99900CA8907 /* diagnosic */ = {
			isa = PBXGroup;
			children = (
				8C3DC06F2E49D99900CA8907 /* firmware */,
				8C3DC0702E49D99900CA8907 /* profile */,
				8C3DC0712E49D99900CA8907 /* signature */,
				8C3DC0722E49D99900CA8907 /* vkeylicensepack */,
			);
			name = diagnosic;
			path = assets/diagnosic;
			sourceTree = "<group>";
		};
		8C79C2C628E2F96500DDC88B = {
			isa = PBXGroup;
			children = (
				8C79C2D128E2F96500DDC88B /* VDS_issue */,
				8C79C2D028E2F96500DDC88B /* Products */,
				8C79C2E628E2FD0600DDC88B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8C79C2D028E2F96500DDC88B /* Products */ = {
			isa = PBXGroup;
			children = (
				8C79C2CF28E2F96500DDC88B /* VDS_issue.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8C79C2D128E2F96500DDC88B /* VDS_issue */ = {
			isa = PBXGroup;
			children = (
				8C3DC0732E49D99900CA8907 /* diagnosic */,
				8C79C2D228E2F96500DDC88B /* AppDelegate.swift */,
				8CA91E9E2E49B98400035F09 /* VKeyExtension.h */,
				8CA91E9F2E49B98400035F09 /* VKeyExtension.m */,
				8C79C2D628E2F96500DDC88B /* ViewController.swift */,
				8C79C2D828E2F96500DDC88B /* Main.storyboard */,
				8C79C2DB28E2F96600DDC88B /* Assets.xcassets */,
				8C79C2DD28E2F96600DDC88B /* LaunchScreen.storyboard */,
				8C79C2E028E2F96600DDC88B /* Info.plist */,
				8C1CFD7829261F0600C40516 /* deleteLater.m */,
				8C1CFD7729261F0600C40516 /* VDS_issue-Bridging-Header.h */,
			);
			path = VDS_issue;
			sourceTree = "<group>";
		};
		8C79C2E628E2FD0600DDC88B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8CFCE45E2D5073D100BD5176 /* VGuard.xcframework */,
				8CFCE45F2D5073D100BD5176 /* VosWrapper.xcframework */,
				8C79C31228E301E800DDC88B /* UIKit.framework */,
				8C79C31028E301DF00DDC88B /* SystemConfiguration.framework */,
				8C79C30E28E301D500DDC88B /* Security.framework */,
				8C79C30C28E301CA00DDC88B /* MobileCoreServices.framework */,
				8C79C30A28E301BB00DDC88B /* Foundation.framework */,
				8C79C30828E301B500DDC88B /* CoreLocation.framework */,
				8C79C30628E301AB00DDC88B /* CoreGraphics.framework */,
				8C79C30428E3019800DDC88B /* libsqlite3.tbd */,
				8C79C30228E3015D00DDC88B /* libz.tbd */,
				8C79C30028E3014A00DDC88B /* libc++.tbd */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8C79C2CE28E2F96500DDC88B /* VDS_issue */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8C79C2E328E2F96600DDC88B /* Build configuration list for PBXNativeTarget "VDS_issue" */;
			buildPhases = (
				8C79C2CB28E2F96500DDC88B /* Sources */,
				8C79C2CC28E2F96500DDC88B /* Frameworks */,
				8C79C2CD28E2F96500DDC88B /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = VDS_issue;
			productName = VDS_issue;
			productReference = 8C79C2CF28E2F96500DDC88B /* VDS_issue.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8C79C2C728E2F96500DDC88B /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1340;
				LastUpgradeCheck = 1340;
				TargetAttributes = {
					8C79C2CE28E2F96500DDC88B = {
						CreatedOnToolsVersion = 13.4.1;
						LastSwiftMigration = 1400;
					};
				};
			};
			buildConfigurationList = 8C79C2CA28E2F96500DDC88B /* Build configuration list for PBXProject "VDS_issue" */;
			compatibilityVersion = "Xcode 13.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8C79C2C628E2F96500DDC88B;
			productRefGroup = 8C79C2D028E2F96500DDC88B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8C79C2CE28E2F96500DDC88B /* VDS_issue */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8C79C2CD28E2F96500DDC88B /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8C3DC0742E49D99900CA8907 /* firmware in Resources */,
				8C3DC0752E49D99900CA8907 /* profile in Resources */,
				8C3DC0762E49D99900CA8907 /* signature in Resources */,
				8C3DC0772E49D99900CA8907 /* vkeylicensepack in Resources */,
				8C79C2DF28E2F96600DDC88B /* LaunchScreen.storyboard in Resources */,
				8C79C2DC28E2F96600DDC88B /* Assets.xcassets in Resources */,
				8C79C2DA28E2F96500DDC88B /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8C79C2CB28E2F96500DDC88B /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				8CA91EA02E49B98400035F09 /* VKeyExtension.m in Sources */,
				8C79C2D728E2F96500DDC88B /* ViewController.swift in Sources */,
				8C79C2D328E2F96500DDC88B /* AppDelegate.swift in Sources */,
				8C1CFD7929261F0600C40516 /* deleteLater.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		8C79C2D828E2F96500DDC88B /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8C79C2D928E2F96500DDC88B /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		8C79C2DD28E2F96600DDC88B /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				8C79C2DE28E2F96600DDC88B /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		8C79C2E128E2F96600DDC88B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8C79C2E228E2F96600DDC88B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		8C79C2E428E2F96600DDC88B /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)",
					"$(PROJECT_DIR)/VDS_issue/Frameworks",
					"$(PROJECT_DIR)/VDS_issue/Frameworks/V-Tap.xcframework/ios-arm64_armv7",
					"$(PROJECT_DIR)/VDS_issue/Frameworks/V-Tap.xcframework/ios-i386_x86_64-simulator",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VDS_issue/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "VDS_issue/VDS_issue-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		8C79C2E528E2F96600DDC88B /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 63DA3SWC73;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)",
					"$(PROJECT_DIR)/VDS_issue/Frameworks",
					"$(PROJECT_DIR)/VDS_issue/Frameworks/V-Tap.xcframework/ios-arm64_armv7",
					"$(PROJECT_DIR)/VDS_issue/Frameworks/V-Tap.xcframework/ios-i386_x86_64-simulator",
				);
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VDS_issue/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_BUNDLE_IDENTIFIER = "com.v-key.diagnosis";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "VDS_issue/VDS_issue-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8C79C2CA28E2F96500DDC88B /* Build configuration list for PBXProject "VDS_issue" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C79C2E128E2F96600DDC88B /* Debug */,
				8C79C2E228E2F96600DDC88B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8C79C2E328E2F96600DDC88B /* Build configuration list for PBXNativeTarget "VDS_issue" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8C79C2E428E2F96600DDC88B /* Debug */,
				8C79C2E528E2F96600DDC88B /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8C79C2C728E2F96500DDC88B /* Project object */;
}
